package id.co.bri.brimo.presenters.inputpasswordnewskin

import id.co.bri.brimo.contract.IPresenter.inputpasswordnewskin.IInputPasswordNewSkinPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.inputpasswordnewskin.IInputPasswordNewSkinView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.LoginErrorData
import id.co.bri.brimo.models.apimodel.request.ChangeDeviceRequest
import id.co.bri.brimo.models.apimodel.request.DetailPromoRequest
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse
import id.co.bri.brimo.models.apimodel.response.LoginResponse
import id.co.bri.brimo.models.apimodel.response.PromoResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import java.util.concurrent.TimeUnit

class InputPasswordNewSkinPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IInputPasswordNewSkinPresenter<V> where V : IMvpView, V : IInputPasswordNewSkinView {
    private var urlLogin = ""
    private var urlPromo = ""
    private var location = ""
    private var urlChange = ""
    private var isChangeDevice = false

    override fun setUrlLogin(url: String) {
        urlLogin = url
    }

    override fun setDetailItemPromoUrl(detailItemUrl: String?) {
        urlPromo = detailItemUrl ?: ""
    }

    override fun submitLogin() {
        isChangeDevice = false
        updateChangeDeviceFlag(isChangeDevice)
        if (urlLogin.isEmpty() || !isViewAttached) return

        var username = brImoPrefRepository.userAlias

        view.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val finalUsername = username
        val disposable: Disposable = apiSource.validateUserLogin(
            urlLogin,
            finalUsername,
            view.password,
            location,
            seqNum
        )
            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
            .subscribeOn(schedulerProvider.single())
            .observeOn(schedulerProvider.mainThread())
            .subscribeWith(object : ApiObserver(view, seqNum) {
                override fun onFailureHttp(errorMessage: String) {
                    getView().hideProgress()
                    getView().onException(errorMessage,"InputPass")
                }

                override fun onApiCallSuccess(response: RestResponse) {
                    getView().hideProgress()
                    val loginResponse = response.getData(LoginResponse::class.java)
                    val fullName = loginResponse.fullName ?: ""

                    when (response.code) {
                        "08" -> {
                            //update flag login
                            updateLoginFlag(true)
                            //update nickname
                            brImoPrefRepository.saveNickname(loginResponse.nickname)
                            //update fullname
                            brImoPrefRepository.saveFullName(fullName)
                            //update reference number
                            brImoPrefRepository.saveUsername(loginResponse.username)
                            //update reference number
                            brImoPrefRepository.saveUserType(loginResponse.typeUser)
                            //update first name
                            val firstName = fullName.substringBefore(" ")
                            brImoPrefRepository.saveFirstName(firstName)
                            //update email
                            brImoPrefRepository.saveEmail(loginResponse.email)
                            //update phone
                            brImoPrefRepository.savePhone(loginResponse.phone)
                            //update last name
                            val lastName = fullName.substringAfter(" ", "")
                            brImoPrefRepository.saveLastName(lastName)


                            //update userr type
                            brImoPrefRepository.saveUserType(Constant.IB_TYPE)
                            // simpen user-alias
                            //on login success
                            getView().onSuccessLogin()
                        }

                        Constant.RE_CHECKING_DEVICE -> {
                            //parsing data akan ada disini
                            brImoPrefRepository.saveTokenKey(loginResponse.tokenKey)
                            //getView().onSubmitSuccess(loginResponse);
                            brImoPrefRepository.saveUsername(loginResponse.username)
                            updateLoginFlag(true)
                            brImoPrefRepository.saveUserType(Constant.IB_TYPE)
                            getView().onSuccessLogin()
                        }
//
                        Constant.RE_CHANGE_DEVICE -> {
                            brImoPrefRepository.saveUpdateTokenFirebase(false)
                            getView().onDeviceChanged(response.desc, loginResponse)
                            isChangeDevice = true
                            updateChangeDeviceFlag(isChangeDevice)
                        }

//                        Constant.RE_CREATE_PIN -> getView().onCreatePin()
//                        Constant.RE01 -> getView().onChangeUsername(response.desc)
                        Constant.RE_CHANGE_DEVICE_MNV -> {
                            brImoPrefRepository.saveUpdateTokenFirebase(false)
                            getView().onChangeMNV(response.desc, loginResponse)
                        }
                    }
//                    brImoPrefRepository.saveUserAlias(finalUsername)
//                    brImoPrefRepository.saveNickname(loginResponse.nickname)
                }

                override fun onApiCallError(restResponse: RestResponse) {
                    getView().hideProgress()
                    if (restResponse.code == Constant.RE_LOGIN_EXCEED) {
                        getView().onExceptionLoginExceed(
                            restResponse.getData(
                                ExceptionResponse::class.java
                            )
                        )
                    } else if(restResponse.code == Constant.RE12){
                        val errorData = restResponse.getData(LoginErrorData::class.java)
                        getView().showError(true, restResponse.desc,errorData)
                    } else getView().onException(restResponse.desc)
                }
            })
        compositeDisposable.add(disposable)
    }


    override fun loginFingerprint() {
        if (view != null) {
            view.showProgress()
            val username = brImoPrefRepository.username
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable =
                apiSource.validateUserLoginFingerprint(urlLogin, username, location, seqNum)
                    .subscribeOn(schedulerProvider.single())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage,"Biometric")
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val loginResponse = response.getData(LoginResponse::class.java)
                            when (response.code) {
                                Constant.RE_SUCCESS -> {
                                    //update flag login
                                    updateLoginFlag(true)
                                    //to dashboard
                                    getView().onSuccessLogin()
                                }

//                                Constant.RE_CHECKING_DEVICE -> {
//                                    //parsing data akan ada disini
//                                    brImoPrefRepository.saveTokenKey(loginResponse.tokenKey)
//                                    getView().onSubmitLoginSuccess(loginResponse)
//                                }
//
                                Constant.RE_CHANGE_DEVICE -> {
                                    getView().onDeviceChanged(response.desc, loginResponse)
                                    isChangeDevice = true
                                    updateChangeDeviceFlag(isChangeDevice)
                                }
//
//                                Constant.RE_CREATE_PIN -> getView().onCreatePin()
//                                Constant.RE01 -> getView().onChangeUsername(response.desc)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            val code = restResponse.code
                            val desc = restResponse.desc

                            if (code.equals(
                                    RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                    ignoreCase = true
                                )
                            ) {
                                getView().onSessionEnd(desc)
                            } else if (code.equals(
                                    RestResponse.ResponseCodeEnum.RC_12.value,
                                    ignoreCase = true
                                )
                            ) {
//                                getView().onException12()
                            } else if (code == RestResponse.ResponseCodeEnum.RC_99.value) {
                                getView().onException99(desc)
                            } else if (code == RestResponse.ResponseCodeEnum.RC_LOGIN_EXCEED.value) {
                                getView().onExceptionLoginExceed(
                                    restResponse.getData(
                                        ExceptionResponse::class.java
                                    )
                                )
                            } else {
                                getView().onException(desc)
                            }
                        }
                    })

            compositeDisposable.add(disposable)
        }
    }

    override fun getLocation(location: String) {
        this.location = location
    }

    override fun getStatusUpdateBio(): Boolean {
        return brImoPrefRepository.statusUpdateBio
    }

    override fun getBioChanged(): Boolean {
        return brImoPrefRepository.statusBioChange
    }

    override fun getStatusAktivasi(): Boolean {
        return brImoPrefRepository.statusAktivasi
    }

    override fun isBiometricLockoutPermanently(): Boolean {
        return brImoPrefRepository.isBiometricLockoutPermanently
    }

    override fun updateStatusAktivasi(statusAktivasi: Boolean) {
        brImoPrefRepository.saveStatusAktivasi(statusAktivasi)
    }

    override fun setBioChangedDialogShown(statusBioChangeShown: Boolean) {
        brImoPrefRepository.isBioChangedDialogShown = statusBioChangeShown
    }

    override fun isBioChangedDialogShown(): Boolean {
        return brImoPrefRepository.isBioChangedDialogShown
    }

    override fun getBioType(): String {
        return brImoPrefRepository.biometricType
    }

    override fun getNameOfUser(): String {
        return brImoPrefRepository.userAlias
    }

    override fun getFirstNameOfUser(): String {
        return brImoPrefRepository.firstName
    }

    override fun getNicknameOfUser(): String {
        return brImoPrefRepository.nickname
    }

    override fun updateChangeDeviceFlag(isChangeDevice: Boolean) {
        brImoPrefRepository.saveChangeDeviceFlag(isChangeDevice)
    }

    override fun getChangeDeviceFlag(): Boolean {
        return brImoPrefRepository.changeDeviceFlag
    }

    override fun getDetailPromoItem(id: String?) {
        if (urlPromo.isEmpty() || !isViewAttached()) return

        view?.showProgress()
        val seqNum = brImoPrefRepository.seqNumber
        val detailPromoRequest = DetailPromoRequest(id)

        compositeDisposable.add(
            apiSource.getData(urlPromo, detailPromoRequest, seqNum)
                .subscribeOn(schedulerProvider.single())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {

                    override fun onFailureHttp(errorMessage: String?) {
                        view?.hideProgress()
                        view?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        view?.hideProgress()
                        val responseBriva = response.getData(PromoResponse::class.java)
                        getView()?.onSuccessGetDetailItem(responseBriva)

                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        view?.hideProgress()
                        when (restResponse.code) {
                            RestResponse.ResponseCodeEnum.RC_SESSION_END.value ->
                                view?.onSessionEnd(restResponse.desc)
                            RestResponse.ResponseCodeEnum.RC_99.value ->
                                view?.onException99(restResponse.desc)
                            else -> view?.onException(restResponse.desc)
                        }
                    }
                })
        )
    }

    override fun changeDevice(refNum: String) {
        if (view != null) {
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable =
                apiSource.getData(urlChange, ChangeDeviceRequest("", refNum), seqNum)
                    .subscribeOn(schedulerProvider.single())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(object : ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val loginResponse = response.getData(LoginResponse::class.java)
                            if (response.code == Constant.RE_SUCCESS) {
                                //parsing data akan ada disini
                                brImoPrefRepository.saveTokenKey(loginResponse.tokenKey)
                                getView().onChangeDevice(loginResponse.otpExpiredInSecond)
                            } else if (response.code == Constant.RE_CHANGE_DEVICE_MNV) {
                                brImoPrefRepository.saveUpdateTokenFirebase(false)
                                getView().onChangeMNV(response.desc, loginResponse)
                            }
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            if (restResponse.code.equals(
                                    RestResponse.ResponseCodeEnum.RC_SESSION_END.value,
                                    ignoreCase = true
                                )
                            ) getView().onSessionEnd(restResponse.desc)
                            else if (restResponse.code == RestResponse.ResponseCodeEnum.RC_99.value) {
                                getView().onException99(restResponse.desc)
                            } else getView().onException(restResponse.desc)
                        }
                    })

            compositeDisposable.add(disposable)
        }
    }

    override fun setUrlChange(url: String) {
        this.urlChange = url
    }

    override fun updateBioChange(statusBioChange: Boolean) {
        brImoPrefRepository.saveStatusBioChange(statusBioChange)
    }

    override fun updateStatusUpdateBio(statusUpdate: Boolean) {
        brImoPrefRepository.saveStatusUpdateBio(statusUpdate)
    }
}